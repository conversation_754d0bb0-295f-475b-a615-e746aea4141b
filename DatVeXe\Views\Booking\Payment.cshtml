@model DatVeXe.Models.PaymentSelectionViewModel
@{
    ViewData["Title"] = "Thanh toán";
}

<div class="container py-4">
    <!-- Alert Messages -->
    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i>
            @TempData["Error"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }
    @if (TempData["Warning"] != null)
    {
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-circle me-2"></i>
            @TempData["Warning"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <!-- Progress Steps -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="progress-steps">
                <div class="step completed">
                    <div class="step-number">1</div>
                    <div class="step-label"><PERSON><PERSON><PERSON> chuyến</div>
                </div>
                <div class="step completed">
                    <div class="step-number">2</div>
                    <div class="step-label">Chọn ghế</div>
                </div>
                <div class="step completed">
                    <div class="step-number">3</div>
                    <div class="step-label">Thông tin</div>
                </div>
                <div class="step active">
                    <div class="step-number">4</div>
                    <div class="step-label">Thanh toán</div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Booking Summary -->
        <div class="col-lg-4 mb-4">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-receipt me-2"></i>Chi tiết đặt vé
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Trip Info -->
                    <div class="booking-summary">
                        <div class="summary-item">
                            <div class="summary-label">
                                <i class="bi bi-geo-alt text-primary me-1"></i>Tuyến đường
                            </div>
                            <div class="summary-value">
                                @Model.ChuyenXe.DiemDiDisplay → @Model.ChuyenXe.DiemDenDisplay
                            </div>
                        </div>

                        <div class="summary-item">
                            <div class="summary-label">
                                <i class="bi bi-calendar-event text-info me-1"></i>Thời gian
                            </div>
                            <div class="summary-value">
                                @Model.ChuyenXe.NgayKhoiHanh.ToString("dd/MM/yyyy HH:mm")
                            </div>
                        </div>

                        <div class="summary-item">
                            <div class="summary-label">
                                <i class="bi bi-square-fill text-success me-1"></i>Ghế ngồi
                            </div>
                            <div class="summary-value">
                                Ghế @Model.ChoNgoi.SoGhe (@Model.ChoNgoi.LoaiGhe)
                            </div>
                        </div>

                        <div class="summary-item">
                            <div class="summary-label">
                                <i class="bi bi-person text-warning me-1"></i>Hành khách
                            </div>
                            <div class="summary-value">
                                @Model.TenKhach
                                <br><small class="text-muted">@Model.SoDienThoai</small>
                            </div>
                        </div>

                        <hr>

                        <!-- Price Breakdown -->
                        <div class="price-breakdown">
                            <div class="price-item">
                                <span>Giá vé</span>
                                <span>@string.Format("{0:N0}", Model.TongTien) VNĐ</span>
                            </div>
                            
                            @if (Model.PhiDichVu > 0)
                            {
                                <div class="price-item">
                                    <span>Phí dịch vụ</span>
                                    <span>@string.Format("{0:N0}", Model.PhiDichVu) VNĐ</span>
                                </div>
                            }

                            @if (Model.GiamGia > 0)
                            {
                                <div class="price-item text-success">
                                    <span>Giảm giá</span>
                                    <span>-@string.Format("{0:N0}", Model.GiamGia) VNĐ</span>
                                </div>
                            }

                            <hr>

                            <div class="price-total">
                                <span class="fw-bold">Tổng thanh toán</span>
                                <span class="fw-bold text-success fs-4">@string.Format("{0:N0}", Model.ThanhTien) VNĐ</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Promotion Code -->
            <div class="card shadow-sm border-0 mt-3">
                <div class="card-header bg-warning text-dark">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-tag me-2"></i>Mã khuyến mãi
                    </h6>
                </div>
                <div class="card-body">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Nhập mã khuyến mãi" id="promoCode">
                        <button class="btn btn-outline-primary" type="button" id="applyPromo">
                            <i class="bi bi-check-circle me-1"></i>Áp dụng
                        </button>
                    </div>
                    <small class="text-muted mt-2 d-block">
                        <i class="bi bi-info-circle me-1"></i>Nhập mã để được giảm giá
                    </small>
                </div>
            </div>
        </div>

        <!-- Payment Methods -->
        <div class="col-lg-8">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-credit-card me-2"></i>Chọn phương thức thanh toán
                    </h5>
                </div>
                <div class="card-body p-4">
                    <form asp-action="Payment" asp-route-sessionId="@ViewContext.RouteData.Values["sessionId"]" method="post" id="paymentForm">
                        <input type="hidden" asp-for="TongTien" />
                        <input type="hidden" asp-for="PhiDichVu" />
                        <input type="hidden" asp-for="ThanhTien" />
                        <input type="hidden" asp-for="GiamGia" />
                        <input type="hidden" asp-for="MaKhuyenMai" />

                        <!-- Payment Methods -->
                        <div class="payment-methods mb-4">
                            <div class="row g-3">
                                <!-- Thanh toán tại quầy -->
                                <div class="col-md-6">
                                    <div class="payment-option">
                                        <input type="radio" asp-for="PhuongThucThanhToan" value="@((int)PhuongThucThanhToan.TaiQuay)" 
                                               id="payment_counter" class="payment-radio" />
                                        <label for="payment_counter" class="payment-label">
                                            <div class="payment-icon">
                                                <i class="bi bi-shop text-primary"></i>
                                            </div>
                                            <div class="payment-info">
                                                <div class="payment-title">Thanh toán tại quầy</div>
                                                <div class="payment-desc">Thanh toán trực tiếp tại bến xe</div>
                                            </div>
                                            <div class="payment-check">
                                                <i class="bi bi-check-circle"></i>
                                            </div>
                                        </label>
                                    </div>
                                </div>

                                <!-- VNPay -->
                                <div class="col-md-6">
                                    <div class="payment-option">
                                        <input type="radio" asp-for="PhuongThucThanhToan" value="@((int)PhuongThucThanhToan.VNPay)" 
                                               id="payment_vnpay" class="payment-radio" />
                                        <label for="payment_vnpay" class="payment-label">
                                            <div class="payment-icon">
                                                <i class="bi bi-credit-card text-danger"></i>
                                            </div>
                                            <div class="payment-info">
                                                <div class="payment-title">VNPay</div>
                                                <div class="payment-desc">Thẻ ATM, Visa, Mastercard</div>
                                            </div>
                                            <div class="payment-check">
                                                <i class="bi bi-check-circle"></i>
                                            </div>
                                        </label>
                                    </div>
                                </div>

                                <!-- MoMo -->
                                <div class="col-md-6">
                                    <div class="payment-option">
                                        <input type="radio" asp-for="PhuongThucThanhToan" value="@((int)PhuongThucThanhToan.MoMo)" 
                                               id="payment_momo" class="payment-radio" />
                                        <label for="payment_momo" class="payment-label">
                                            <div class="payment-icon">
                                                <i class="bi bi-phone text-success"></i>
                                            </div>
                                            <div class="payment-info">
                                                <div class="payment-title">Ví MoMo</div>
                                                <div class="payment-desc">Thanh toán qua ví điện tử MoMo</div>
                                            </div>
                                            <div class="payment-check">
                                                <i class="bi bi-check-circle"></i>
                                            </div>
                                        </label>
                                    </div>
                                </div>

                                <!-- ZaloPay -->
                                <div class="col-md-6">
                                    <div class="payment-option">
                                        <input type="radio" asp-for="PhuongThucThanhToan" value="@((int)PhuongThucThanhToan.ZaloPay)" 
                                               id="payment_zalopay" class="payment-radio" />
                                        <label for="payment_zalopay" class="payment-label">
                                            <div class="payment-icon">
                                                <i class="bi bi-wallet2 text-info"></i>
                                            </div>
                                            <div class="payment-info">
                                                <div class="payment-title">ZaloPay</div>
                                                <div class="payment-desc">Thanh toán qua ví ZaloPay</div>
                                            </div>
                                            <div class="payment-check">
                                                <i class="bi bi-check-circle"></i>
                                            </div>
                                        </label>
                                    </div>
                                </div>

                                <!-- Chuyển khoản -->
                                <div class="col-md-12">
                                    <div class="payment-option">
                                        <input type="radio" asp-for="PhuongThucThanhToan" value="@((int)PhuongThucThanhToan.ChuyenKhoan)" 
                                               id="payment_transfer" class="payment-radio" />
                                        <label for="payment_transfer" class="payment-label">
                                            <div class="payment-icon">
                                                <i class="bi bi-bank text-warning"></i>
                                            </div>
                                            <div class="payment-info">
                                                <div class="payment-title">Chuyển khoản ngân hàng</div>
                                                <div class="payment-desc">Chuyển khoản trực tiếp qua ngân hàng</div>
                                            </div>
                                            <div class="payment-check">
                                                <i class="bi bi-check-circle"></i>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <span asp-validation-for="PhuongThucThanhToan" class="text-danger"></span>
                        </div>

                        <!-- Terms and Conditions -->
                        <div class="terms-section mb-4">
                            <div class="card bg-light border-0">
                                <div class="card-body">
                                    <div class="form-check">
                                        <input asp-for="DongYDieuKhoan" class="form-check-input" type="checkbox" />
                                        <label asp-for="DongYDieuKhoan" class="form-check-label">
                                            Tôi đồng ý với 
                                            <a href="#" class="text-primary" data-bs-toggle="modal" data-bs-target="#termsModal">
                                                điều khoản sử dụng
                                            </a> 
                                            và 
                                            <a href="#" class="text-primary">chính sách bảo mật</a>
                                        </label>
                                    </div>
                                    <span asp-validation-for="DongYDieuKhoan" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="javascript:history.back()" class="btn btn-outline-secondary btn-lg px-4">
                                        <i class="bi bi-arrow-left me-2"></i>Quay lại
                                    </a>
                                    <button type="submit" class="btn btn-success btn-lg px-5" id="paymentBtn">
                                        <i class="bi bi-credit-card me-2"></i>Thanh toán @string.Format("{0:N0}", Model.ThanhTien) VNĐ
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        $(document).ready(function() {
            // Payment method selection
            $('.payment-radio').change(function() {
                $('.payment-option').removeClass('selected');
                $(this).closest('.payment-option').addClass('selected');
                
                // Update service fee based on payment method
                updateServiceFee();
            });

            // Form validation
            $('#paymentForm').on('submit', function(e) {
                if (!$(this).valid()) {
                    e.preventDefault();
                    return false;
                }
                
                if (!$('.payment-radio:checked').length) {
                    e.preventDefault();
                    alert('Vui lòng chọn phương thức thanh toán');
                    return false;
                }
                
                if (!$('#DongYDieuKhoan').is(':checked')) {
                    e.preventDefault();
                    alert('Vui lòng đồng ý với điều khoản sử dụng');
                    return false;
                }
                
                // Show loading state
                $('#paymentBtn').prop('disabled', true)
                    .html('<i class="spinner-border spinner-border-sm me-2"></i>Đang xử lý...');

                // Show loading overlay
                showLoadingOverlay();

                return true;
            });

            // Promo code application
            $('#applyPromo').click(function() {
                const promoCode = $('#promoCode').val().trim();
                if (!promoCode) {
                    alert('Vui lòng nhập mã khuyến mãi');
                    return;
                }
                
                // TODO: Implement promo code validation
                alert('Chức năng mã khuyến mãi đang được phát triển');
            });

            function updateServiceFee() {
                const selectedMethod = $('.payment-radio:checked').val();
                let serviceFee = 0;

                // Calculate service fee based on payment method
                switch(selectedMethod) {
                    case '@((int)PhuongThucThanhToan.VNPay)':
                    case '@((int)PhuongThucThanhToan.MoMo)':
                    case '@((int)PhuongThucThanhToan.ZaloPay)':
                        serviceFee = Math.round(@Model.TongTien * 0.02); // 2% service fee
                        break;
                    default:
                        serviceFee = 0;
                }

                // Update UI (if needed)
                // This is a placeholder for dynamic fee calculation
            }

            // Add loading overlay function
            function showLoadingOverlay() {
                if ($('#loadingOverlay').length === 0) {
                    $('body').append(`
                        <div id="loadingOverlay" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                             background: rgba(0,0,0,0.5); z-index: 9999; display: flex; align-items: center; justify-content: center;">
                            <div style="background: white; padding: 30px; border-radius: 15px; text-align: center; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
                                <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <div class="h5 mb-0">Đang xử lý thanh toán...</div>
                                <div class="text-muted">Vui lòng không đóng trang này</div>
                            </div>
                        </div>
                    `);
                }
            }
        });
    </script>
}

<style>
    .progress-steps {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 2rem;
    }

    .step {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin: 0 2rem;
        position: relative;
    }

    .step:not(:last-child)::after {
        content: '';
        position: absolute;
        top: 20px;
        left: 100%;
        width: 4rem;
        height: 2px;
        background: #dee2e6;
        z-index: -1;
    }

    .step.completed::after,
    .step.active::after {
        background: #28a745;
    }

    .step-number {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #dee2e6;
        color: #6c757d;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }

    .step.completed .step-number {
        background: #28a745;
        color: white;
    }

    .step.active .step-number {
        background: #007bff;
        color: white;
    }

    .step-label {
        font-size: 0.875rem;
        color: #6c757d;
        font-weight: 500;
    }

    .step.completed .step-label,
    .step.active .step-label {
        color: #495057;
        font-weight: 600;
    }

    .summary-item {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid #f8f9fa;
    }

    .summary-label {
        font-weight: 500;
        color: #6c757d;
        flex: 0 0 40%;
    }

    .summary-value {
        text-align: right;
        flex: 1;
        font-weight: 500;
    }

    .price-breakdown {
        margin-top: 1rem;
    }

    .price-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;
    }

    .price-total {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 1rem;
    }

    .payment-option {
        position: relative;
        margin-bottom: 1rem;
    }

    .payment-radio {
        position: absolute;
        opacity: 0;
        pointer-events: none;
    }

    .payment-label {
        display: flex;
        align-items: center;
        padding: 1rem;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        cursor: pointer;
        transition: all 0.3s ease;
        background: white;
    }

    .payment-label:hover {
        border-color: #007bff;
        background: #f8f9ff;
    }

    .payment-option.selected .payment-label {
        border-color: #007bff;
        background: #f8f9ff;
    }

    .payment-icon {
        flex: 0 0 50px;
        text-align: center;
        font-size: 1.5rem;
    }

    .payment-info {
        flex: 1;
        margin-left: 1rem;
    }

    .payment-title {
        font-weight: 600;
        margin-bottom: 0.25rem;
    }

    .payment-desc {
        font-size: 0.875rem;
        color: #6c757d;
    }

    .payment-check {
        flex: 0 0 30px;
        text-align: center;
        color: #28a745;
        font-size: 1.25rem;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .payment-option.selected .payment-check {
        opacity: 1;
    }

    .card {
        border-radius: 15px;
        overflow: hidden;
    }

    .btn-lg {
        border-radius: 10px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-lg:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
</style>
