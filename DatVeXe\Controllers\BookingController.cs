using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using DatVeXe.Models;
using DatVeXe.Services;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace DatVeXe.Controllers
{
    public class BookingController : Controller
    {
        private readonly DatVeXeContext _context;
        private readonly IEmailService _emailService;
        private readonly ISMSService _smsService;
        private readonly IPaymentService _paymentService;
        private readonly ILogger<BookingController> _logger;

        public BookingController(DatVeXeContext context, IEmailService emailService, ISMSService smsService,
            IPaymentService paymentService, ILogger<BookingController> logger)
        {
            _context = context;
            _emailService = emailService;
            _smsService = smsService;
            _paymentService = paymentService;
            _logger = logger;
        }

        // GET: Booking/Search - Tìm kiếm chuyến xe
        public async Task<IActionResult> Search()
        {
            var viewModel = new BookingSearchViewModel();
            
            // Load danh sách điểm đi/đến
            var diemDiList = await _context.TuyenDuongs
                .Where(t => t.TrangThaiHoatDong)
                .Select(t => t.DiemDi)
                .Distinct()
                .OrderBy(d => d)
                .ToListAsync();

            var diemDenList = await _context.TuyenDuongs
                .Where(t => t.TrangThaiHoatDong)
                .Select(t => t.DiemDen)
                .Distinct()
                .OrderBy(d => d)
                .ToListAsync();

            ViewBag.DiemDiList = diemDiList;
            ViewBag.DiemDenList = diemDenList;

            return View(viewModel);
        }

        // POST: Booking/Search - Xử lý tìm kiếm
        [HttpPost]
        public async Task<IActionResult> Search(BookingSearchViewModel model)
        {
            if (!ModelState.IsValid)
            {
                // Reload dropdown data
                var diemDiList = await _context.TuyenDuongs
                    .Where(t => t.TrangThaiHoatDong)
                    .Select(t => t.DiemDi)
                    .Distinct()
                    .OrderBy(d => d)
                    .ToListAsync();

                var diemDenList = await _context.TuyenDuongs
                    .Where(t => t.TrangThaiHoatDong)
                    .Select(t => t.DiemDen)
                    .Distinct()
                    .OrderBy(d => d)
                    .ToListAsync();

                ViewBag.DiemDiList = diemDiList;
                ViewBag.DiemDenList = diemDenList;
                return View(model);
            }

            // Tìm kiếm chuyến xe
            var query = _context.ChuyenXes
                .Include(c => c.Xe)
                .Include(c => c.TuyenDuong)
                .Include(c => c.Ves)
                .Where(c => c.TrangThaiChuyenXe == TrangThaiChuyenXe.HoatDong)
                .AsQueryable();

            if (!string.IsNullOrEmpty(model.DiemDi))
            {
                query = query.Where(c => c.DiemDi == model.DiemDi || 
                                        (c.TuyenDuong != null && c.TuyenDuong.DiemDi == model.DiemDi));
            }

            if (!string.IsNullOrEmpty(model.DiemDen))
            {
                query = query.Where(c => c.DiemDen == model.DiemDen || 
                                        (c.TuyenDuong != null && c.TuyenDuong.DiemDen == model.DiemDen));
            }

            if (model.NgayDi != default(DateTime))
            {
                var startDate = model.NgayDi.Date;
                var endDate = startDate.AddDays(1);
                query = query.Where(c => c.NgayKhoiHanh >= startDate && c.NgayKhoiHanh < endDate);
            }

            // Bộ lọc nâng cao
            if (model.GioKhoiHanhTu.HasValue)
            {
                query = query.Where(c => c.NgayKhoiHanh.TimeOfDay >= model.GioKhoiHanhTu.Value);
            }

            if (model.GioKhoiHanhDen.HasValue)
            {
                query = query.Where(c => c.NgayKhoiHanh.TimeOfDay <= model.GioKhoiHanhDen.Value);
            }

            if (!string.IsNullOrEmpty(model.LoaiXe))
            {
                query = query.Where(c => c.Xe != null && c.Xe.LoaiXe.Contains(model.LoaiXe));
            }

            if (!string.IsNullOrEmpty(model.NhaXe))
            {
                query = query.Where(c => c.Xe != null && c.Xe.NhaXe != null && c.Xe.NhaXe.Contains(model.NhaXe));
            }

            if (model.GiaTu.HasValue)
            {
                query = query.Where(c => c.Gia >= model.GiaTu.Value);
            }

            if (model.GiaDen.HasValue)
            {
                query = query.Where(c => c.Gia <= model.GiaDen.Value);
            }

            // Chỉ lấy chuyến xe chưa khởi hành
            query = query.Where(c => c.NgayKhoiHanh > DateTime.Now);

            // Áp dụng sắp xếp
            switch (model.SortBy?.ToLower())
            {
                case "gia":
                    query = model.SortOrder == "desc" ?
                        query.OrderByDescending(c => c.Gia) :
                        query.OrderBy(c => c.Gia);
                    break;
                case "nhaxe":
                    query = model.SortOrder == "desc" ?
                        query.OrderByDescending(c => c.Xe!.NhaXe) :
                        query.OrderBy(c => c.Xe!.NhaXe);
                    break;
                default: // NgayKhoiHanh
                    query = model.SortOrder == "desc" ?
                        query.OrderByDescending(c => c.NgayKhoiHanh) :
                        query.OrderBy(c => c.NgayKhoiHanh);
                    break;
            }

            var chuyenXes = await query.ToListAsync();

            // Lọc chuyến xe có đủ ghế trống
            model.KetQua = chuyenXes.Where(c =>
                (c.Xe?.SoGhe ?? 0) - (c.Ves?.Count ?? 0) >= model.SoHanhKhach
            ).ToList();

            model.TotalResults = model.KetQua.Count;
            model.HasSearched = true;

            // Reload dropdown data
            var diemDiListResult = await _context.TuyenDuongs
                .Where(t => t.TrangThaiHoatDong)
                .Select(t => t.DiemDi)
                .Distinct()
                .OrderBy(d => d)
                .ToListAsync();

            var diemDenListResult = await _context.TuyenDuongs
                .Where(t => t.TrangThaiHoatDong)
                .Select(t => t.DiemDen)
                .Distinct()
                .OrderBy(d => d)
                .ToListAsync();

            ViewBag.DiemDiList = diemDiListResult;
            ViewBag.DiemDenList = diemDenListResult;

            return View(model);
        }

        // GET: Booking/SelectTrip/{id} - Chọn chuyến xe và bắt đầu quy trình đặt vé
        public async Task<IActionResult> SelectTrip(int id)
        {
            var chuyenXe = await _context.ChuyenXes
                .Include(c => c.Xe)
                .Include(c => c.TuyenDuong)
                .Include(c => c.Ves)
                .FirstOrDefaultAsync(c => c.ChuyenXeId == id);

            if (chuyenXe == null)
            {
                TempData["Error"] = "Không tìm thấy chuyến xe";
                return RedirectToAction("Search");
            }

            if (chuyenXe.NgayKhoiHanh <= DateTime.Now)
            {
                TempData["Error"] = "Chuyến xe đã khởi hành";
                return RedirectToAction("Search");
            }

            // Tạo session booking mới
            var sessionId = Guid.NewGuid().ToString();
            var bookingStep = new BookingStepViewModel
            {
                CurrentStep = 2, // Chuyển đến bước chọn ghế
                SessionId = sessionId,
                ChuyenXeId = id,
                ChuyenXe = null, // Không lưu entity vào session để tránh circular reference
                TongTien = chuyenXe.Gia
            };

            // Lưu vào session với JsonSerializerOptions để tránh circular reference
            var options = new JsonSerializerOptions
            {
                ReferenceHandler = ReferenceHandler.IgnoreCycles,
                WriteIndented = false
            };
            HttpContext.Session.SetString($"Booking_{sessionId}", JsonSerializer.Serialize(bookingStep, options));

            return RedirectToAction("SelectSeat", new { sessionId });
        }

        // GET: Booking/SelectSeat - Chọn ghế ngồi
        public async Task<IActionResult> SelectSeat(string sessionId)
        {
            var bookingStep = GetBookingFromSession(sessionId);
            if (bookingStep == null || !bookingStep.ChuyenXeId.HasValue)
            {
                TempData["Error"] = "Phiên đặt vé không hợp lệ";
                return RedirectToAction("Search");
            }

            var chuyenXe = await _context.ChuyenXes
                .Include(c => c.Xe)
                .Include(c => c.TuyenDuong)
                .Include(c => c.Ves)
                .ThenInclude(v => v.ChoNgoi)
                .FirstOrDefaultAsync(c => c.ChuyenXeId == bookingStep.ChuyenXeId);

            if (chuyenXe == null)
            {
                TempData["Error"] = "Không tìm thấy chuyến xe";
                return RedirectToAction("Search");
            }

            // Cập nhật ChuyenXe vào booking step
            bookingStep.ChuyenXe = chuyenXe;

            // Lấy danh sách ghế
            var choNgois = await _context.ChoNgois
                .Where(c => c.XeId == chuyenXe.XeId && c.TrangThaiHoatDong)
                .OrderBy(c => c.Hang)
                .ThenBy(c => c.Cot)
                .ToListAsync();

            // Lấy ghế đã đặt
            var veDaDat = chuyenXe.Ves?.Where(v => v.TrangThai == TrangThaiVe.DaDat).ToList() ?? new List<Ve>();

            // Lấy ghế đang được giữ
            var activeReservations = await _context.SeatReservations
                .Where(r => r.ChuyenXeId == chuyenXe.ChuyenXeId &&
                           r.IsActive &&
                           r.ExpiresAt > DateTime.Now)
                .ToListAsync();

            var viewModel = new SeatSelectionViewModel
            {
                ChuyenXeId = chuyenXe.ChuyenXeId,
                ChuyenXe = chuyenXe,
                Xe = chuyenXe.Xe!,
                SessionId = sessionId,
                SoHang = choNgois.Any() ? choNgois.Max(c => c.Hang) : 0,
                SoCot = choNgois.Any() ? choNgois.Max(c => c.Cot) : 0,
                LoaiXe = chuyenXe.Xe?.LoaiXe ?? "",
                DanhSachGhe = choNgois.Select(c => new ChoNgoiViewModel
                {
                    ChoNgoiId = c.ChoNgoiId,
                    SoGhe = c.SoGhe,
                    Hang = c.Hang,
                    Cot = c.Cot,
                    LoaiGhe = c.LoaiGhe,
                    TrangThaiHoatDong = c.TrangThaiHoatDong,
                    DaDat = veDaDat.Any(v => v.ChoNgoiId == c.ChoNgoiId),
                    DangGiu = activeReservations.Any(r => r.ChoNgoiId == c.ChoNgoiId && r.SessionId != sessionId),
                    TenKhachDat = veDaDat.FirstOrDefault(v => v.ChoNgoiId == c.ChoNgoiId)?.TenKhach
                }).ToList()
            };

            return View(viewModel);
        }

        // POST: Booking/SelectSeat - Xử lý chọn ghế
        [HttpPost]
        public async Task<IActionResult> SelectSeat(string sessionId, int seatId)
        {
            var bookingStep = GetBookingFromSession(sessionId);
            if (bookingStep == null || !bookingStep.IsStep1Valid())
            {
                return Json(new { success = false, message = "Phiên đặt vé không hợp lệ" });
            }

            // Kiểm tra ghế có tồn tại và khả dụng
            var choNgoi = await _context.ChoNgois
                .FirstOrDefaultAsync(c => c.ChoNgoiId == seatId && c.TrangThaiHoatDong);

            if (choNgoi == null)
            {
                return Json(new { success = false, message = "Ghế không tồn tại hoặc không khả dụng" });
            }

            // Kiểm tra ghế đã được đặt chưa
            var daDat = await _context.Ves
                .AnyAsync(v => v.ChuyenXeId == bookingStep.ChuyenXeId && 
                              v.ChoNgoiId == seatId && 
                              v.TrangThai == TrangThaiVe.DaDat);

            if (daDat)
            {
                return Json(new { success = false, message = "Ghế đã được đặt" });
            }

            // Kiểm tra ghế đang được giữ bởi session khác
            var dangGiu = await _context.SeatReservations
                .AnyAsync(r => r.ChuyenXeId == bookingStep.ChuyenXeId &&
                              r.ChoNgoiId == seatId &&
                              r.IsActive &&
                              r.ExpiresAt > DateTime.Now &&
                              r.SessionId != sessionId);

            if (dangGiu)
            {
                return Json(new { success = false, message = "Ghế đang được giữ bởi khách hàng khác" });
            }

            // Hủy reservation cũ của session này
            var oldReservations = await _context.SeatReservations
                .Where(r => r.ChuyenXeId == bookingStep.ChuyenXeId &&
                           r.SessionId == sessionId &&
                           r.IsActive)
                .ToListAsync();

            foreach (var old in oldReservations)
            {
                old.IsActive = false;
            }

            // Tạo reservation mới
            var reservation = new SeatReservation
            {
                ChuyenXeId = bookingStep.ChuyenXeId.Value,
                ChoNgoiId = seatId,
                SessionId = sessionId,
                UserEmail = HttpContext.Session.GetString("UserEmail"),
                ReservedAt = DateTime.Now,
                ExpiresAt = DateTime.Now.AddMinutes(5),
                IsActive = true
            };

            _context.SeatReservations.Add(reservation);
            await _context.SaveChangesAsync();

            // Cập nhật booking step
            bookingStep.CurrentStep = 3;
            bookingStep.ChoNgoiId = seatId;
            bookingStep.ChoNgoi = choNgoi;

            // Lưu lại vào session
            HttpContext.Session.SetString($"Booking_{sessionId}", JsonSerializer.Serialize(bookingStep));

            return Json(new { 
                success = true, 
                message = "Đã chọn ghế thành công",
                nextUrl = Url.Action("PassengerInfo", new { sessionId })
            });
        }

        // Helper method để lấy booking từ session
        private BookingStepViewModel? GetBookingFromSession(string sessionId)
        {
            var sessionData = HttpContext.Session.GetString($"Booking_{sessionId}");
            if (string.IsNullOrEmpty(sessionData))
                return null;

            try
            {
                var options = new JsonSerializerOptions
                {
                    ReferenceHandler = ReferenceHandler.IgnoreCycles,
                    WriteIndented = false
                };
                return JsonSerializer.Deserialize<BookingStepViewModel>(sessionData, options);
            }
            catch
            {
                return null;
            }
        }

        // Helper method để lưu booking vào session
        private void SaveBookingToSession(string sessionId, BookingStepViewModel booking)
        {
            var options = new JsonSerializerOptions
            {
                ReferenceHandler = ReferenceHandler.IgnoreCycles,
                WriteIndented = false
            };
            HttpContext.Session.SetString($"Booking_{sessionId}", JsonSerializer.Serialize(booking, options));
        }

        // GET: Booking/PassengerInfo - Nhập thông tin hành khách
        public async Task<IActionResult> PassengerInfo(string sessionId)
        {
            var bookingStep = GetBookingFromSession(sessionId);
            if (bookingStep == null || !bookingStep.IsStep2Valid())
            {
                TempData["Error"] = "Vui lòng chọn ghế trước";
                return RedirectToAction("SelectSeat", new { sessionId });
            }

            // Load fresh data from database
            var chuyenXe = await _context.ChuyenXes
                .Include(c => c.Xe)
                .Include(c => c.TuyenDuong)
                .FirstOrDefaultAsync(c => c.ChuyenXeId == bookingStep.ChuyenXeId);

            var choNgoi = await _context.ChoNgois
                .FirstOrDefaultAsync(c => c.ChoNgoiId == bookingStep.ChoNgoiId);

            if (chuyenXe == null || choNgoi == null)
            {
                TempData["Error"] = "Không tìm thấy thông tin chuyến xe hoặc ghế";
                return RedirectToAction("Search");
            }

            var viewModel = new PassengerInfoViewModel
            {
                ChuyenXeId = bookingStep.ChuyenXeId.Value,
                ChoNgoiId = bookingStep.ChoNgoiId.Value,
                ChuyenXe = chuyenXe,
                ChoNgoi = choNgoi
            };

            // Nếu user đã đăng nhập, tự động điền thông tin và load danh bạ
            int? userId = HttpContext.Session.GetInt32("UserId");
            if (userId.HasValue)
            {
                var user = await _context.NguoiDungs.FindAsync(userId.Value);
                if (user != null)
                {
                    viewModel.TenKhach = user.HoTen;
                    viewModel.SoDienThoai = user.SoDienThoai;
                    viewModel.Email = user.Email;
                }

                // Load danh bạ hành khách
                ViewBag.DanhBaHanhKhach = await _context.DanhBaHanhKhachs
                    .Where(d => d.NguoiDungId == userId.Value)
                    .OrderByDescending(d => d.SoLanSuDung)
                    .ThenBy(d => d.TenHanhKhach)
                    .Take(10)
                    .ToListAsync();
            }

            // Truyền sessionId vào ViewBag
            ViewBag.SessionId = sessionId;

            return View(viewModel);
        }

        // POST: Booking/PassengerInfo - Xử lý thông tin hành khách
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> PassengerInfo(string sessionId,
            [Bind("ChuyenXeId,ChoNgoiId,TenKhach,SoDienThoai,Email,GhiChu,NhanSMS,NhanEmail")] PassengerInfoViewModel model)
        {
            _logger.LogInformation($"PassengerInfo POST called with sessionId: {sessionId}");
            _logger.LogInformation($"Model: TenKhach={model.TenKhach}, SoDienThoai={model.SoDienThoai}");

            var bookingStep = GetBookingFromSession(sessionId);
            if (bookingStep == null || !bookingStep.IsStep2Valid())
            {
                TempData["Error"] = "Phiên đặt vé không hợp lệ";
                return RedirectToAction("Search");
            }

            if (!ModelState.IsValid)
            {
                _logger.LogWarning("ModelState is invalid");
                foreach (var error in ModelState.Values.SelectMany(v => v.Errors))
                {
                    _logger.LogWarning($"Validation error: {error.ErrorMessage}");
                }

                // Reload data for view
                var chuyenXe = await _context.ChuyenXes
                    .Include(c => c.Xe)
                    .Include(c => c.TuyenDuong)
                    .FirstOrDefaultAsync(c => c.ChuyenXeId == bookingStep.ChuyenXeId);

                var choNgoi = await _context.ChoNgois
                    .FirstOrDefaultAsync(c => c.ChoNgoiId == bookingStep.ChoNgoiId);

                model.ChuyenXe = chuyenXe!;
                model.ChoNgoi = choNgoi!;
                return View(model);
            }

            // Cập nhật thông tin hành khách
            bookingStep.CurrentStep = 4;
            bookingStep.TenKhach = model.TenKhach;
            bookingStep.SoDienThoai = model.SoDienThoai;
            bookingStep.Email = model.Email;
            bookingStep.GhiChu = model.GhiChu;

            _logger.LogInformation($"Updated booking step 3 -> 4. TenKhach: {bookingStep.TenKhach}, SoDienThoai: {bookingStep.SoDienThoai}");

            SaveBookingToSession(sessionId, bookingStep);

            _logger.LogInformation($"Redirecting to Payment with sessionId: {sessionId}");
            return RedirectToAction("Payment", new { sessionId });
        }

        // GET: Booking/Payment - Chọn phương thức thanh toán
        public async Task<IActionResult> Payment(string sessionId)
        {
            _logger.LogInformation($"Payment GET called with sessionId: {sessionId}");

            var bookingStep = GetBookingFromSession(sessionId);
            if (bookingStep == null)
            {
                _logger.LogWarning($"BookingStep is null for sessionId: {sessionId}");
                TempData["Error"] = "Phiên đặt vé không hợp lệ";
                return RedirectToAction("Search");
            }

            _logger.LogInformation($"BookingStep found. CurrentStep: {bookingStep.CurrentStep}, TenKhach: {bookingStep.TenKhach}, SoDienThoai: {bookingStep.SoDienThoai}");

            if (!bookingStep.IsStep3Valid())
            {
                _logger.LogWarning($"Step3 validation failed for sessionId: {sessionId}");
                TempData["Error"] = "Vui lòng nhập thông tin hành khách trước";
                return RedirectToAction("PassengerInfo", new { sessionId });
            }

            // Load fresh data from database
            var chuyenXe = await _context.ChuyenXes
                .Include(c => c.Xe)
                .Include(c => c.TuyenDuong)
                .FirstOrDefaultAsync(c => c.ChuyenXeId == bookingStep.ChuyenXeId);

            var choNgoi = await _context.ChoNgois
                .FirstOrDefaultAsync(c => c.ChoNgoiId == bookingStep.ChoNgoiId);

            if (chuyenXe == null || choNgoi == null)
            {
                TempData["Error"] = "Không tìm thấy thông tin chuyến xe hoặc ghế";
                return RedirectToAction("Search");
            }

            var viewModel = new PaymentSelectionViewModel
            {
                ChuyenXe = chuyenXe,
                ChoNgoi = choNgoi,
                TongTien = bookingStep.TongTien,
                PhiDichVu = 0, // Có thể tính phí dịch vụ tùy theo phương thức thanh toán
                ThanhTien = bookingStep.TongTien
            };

            // Set thông tin khách hàng
            viewModel.TenKhach = bookingStep.TenKhach;
            viewModel.SoDienThoai = bookingStep.SoDienThoai;
            viewModel.Email = bookingStep.Email;
            viewModel.GhiChu = bookingStep.GhiChu;
            viewModel.GiaVe = bookingStep.TongTien;

            // Truyền sessionId vào ViewBag
            ViewBag.SessionId = sessionId;

            return View(viewModel);
        }

        // POST: Booking/Payment - Xử lý thanh toán
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Payment(string sessionId, PaymentSelectionViewModel model)
        {
            var bookingStep = GetBookingFromSession(sessionId);
            if (bookingStep == null || !bookingStep.IsStep3Valid())
            {
                TempData["Error"] = "Phiên đặt vé không hợp lệ";
                return RedirectToAction("Search");
            }

            if (!ModelState.IsValid)
            {
                // Reload data for view
                var chuyenXe = await _context.ChuyenXes
                    .Include(c => c.Xe)
                    .Include(c => c.TuyenDuong)
                    .FirstOrDefaultAsync(c => c.ChuyenXeId == bookingStep.ChuyenXeId);

                var choNgoi = await _context.ChoNgois
                    .FirstOrDefaultAsync(c => c.ChoNgoiId == bookingStep.ChoNgoiId);

                model.ChuyenXe = chuyenXe!;
                model.ChoNgoi = choNgoi!;
                model.TongTien = bookingStep.TongTien;
                model.ThanhTien = bookingStep.TongTien;
                return View(model);
            }

            try
            {
                _logger.LogInformation($"Bắt đầu tạo booking cho session {sessionId}");

                // Tạo mã booking
                var maBooking = GenerateTicketCode();

                // Lưu thông tin booking vào session để sử dụng sau
                bookingStep.MaVe = maBooking;

                // Cập nhật session
                HttpContext.Session.SetString($"Booking_{sessionId}", System.Text.Json.JsonSerializer.Serialize(bookingStep));

                _logger.LogInformation($"Đã tạo booking thành công với mã {maBooking}");

                // Tạo giao dịch thanh toán
                var thanhToan = new ThanhToan
                {
                    VeId = 0, // Không liên kết với Ve nữa
                    MaGiaoDich = GenerateTransactionCode(),
                    PhuongThucThanhToan = model.PhuongThucThanhToan,
                    SoTien = model.ThanhTien,
                    TrangThai = model.PhuongThucThanhToan == PhuongThucThanhToan.TaiQuay ?
                               TrangThaiThanhToan.ChoThanhToan : TrangThaiThanhToan.DangXuLy,
                    ThoiGianTao = DateTime.Now,
                    GhiChu = $"Thanh toán booking {maBooking}"
                };

                _context.ThanhToans.Add(thanhToan);
                await _context.SaveChangesAsync();

                // Cập nhật booking step
                bookingStep.MaGiaoDich = thanhToan.MaGiaoDich;
                bookingStep.PhuongThucThanhToan = model.PhuongThucThanhToan;

                SaveBookingToSession(sessionId, bookingStep);

                // Xử lý theo phương thức thanh toán
                if (model.PhuongThucThanhToan == PhuongThucThanhToan.TaiQuay)
                {
                    // Thanh toán tại quầy - hoàn tất ngay
                    _logger.LogInformation($"Thanh toán tại quầy cho booking {maBooking}");
                    TempData["Success"] = "Đặt vé thành công! Vui lòng thanh toán tại quầy khi lên xe.";
                    return RedirectToAction("Confirmation", new { sessionId });
                }
                else
                {
                    // Chuyển hướng đến gateway thanh toán
                    _logger.LogInformation($"Chuyển hướng đến gateway thanh toán cho booking {maBooking}");
                    return RedirectToAction("ProcessPayment", new { sessionId, paymentId = thanhToan.ThanhToanId });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi tạo vé và thanh toán");
                TempData["Error"] = "Có lỗi xảy ra khi xử lý đặt vé. Vui lòng thử lại.";

                // Reload data for view
                var chuyenXe = await _context.ChuyenXes
                    .Include(c => c.Xe)
                    .Include(c => c.TuyenDuong)
                    .FirstOrDefaultAsync(c => c.ChuyenXeId == bookingStep.ChuyenXeId);

                var choNgoi = await _context.ChoNgois
                    .FirstOrDefaultAsync(c => c.ChoNgoiId == bookingStep.ChoNgoiId);

                model.ChuyenXe = chuyenXe!;
                model.ChoNgoi = choNgoi!;
                model.TongTien = bookingStep.TongTien;
                model.ThanhTien = bookingStep.TongTien;
                return View(model);
            }
        }

        // Helper methods
        private string GenerateTicketCode()
        {
            return $"VE{DateTime.Now:yyyyMMddHHmmss}{new Random().Next(100, 999)}";
        }

        private string GenerateTransactionCode()
        {
            return $"TXN{DateTime.Now:yyyyMMddHHmmss}{new Random().Next(1000, 9999)}";
        }

        // GET: Booking/ProcessPayment - Xử lý thanh toán qua gateway
        public async Task<IActionResult> ProcessPayment(string sessionId, int paymentId)
        {
            var bookingStep = GetBookingFromSession(sessionId);
            if (bookingStep == null)
            {
                TempData["Error"] = "Phiên đặt vé không hợp lệ";
                return RedirectToAction("Search");
            }

            var thanhToan = await _context.ThanhToans
                .Include(t => t.Ve)
                .ThenInclude(v => v.ChuyenXe)
                .FirstOrDefaultAsync(t => t.ThanhToanId == paymentId);

            if (thanhToan == null)
            {
                TempData["Error"] = "Không tìm thấy giao dịch thanh toán";
                return RedirectToAction("Search");
            }

            try
            {
                // Tạo payment request
                var paymentRequest = new PaymentRequestViewModel
                {
                    VeId = thanhToan.VeId,
                    PhuongThuc = thanhToan.PhuongThucThanhToan,
                    SoTien = thanhToan.SoTien,
                    ReturnUrl = Url.Action("PaymentCallback", "Booking", new { sessionId }, Request.Scheme),
                    CancelUrl = Url.Action("PaymentCancel", "Booking", new { sessionId }, Request.Scheme),
                    OrderInfo = $"Thanh toan ve {thanhToan.Ve?.MaVe}"
                };

                var paymentResponse = await _paymentService.CreatePaymentAsync(paymentRequest);

                if (paymentResponse.Success && !string.IsNullOrEmpty(paymentResponse.PaymentUrl))
                {
                    // Cập nhật trạng thái thanh toán
                    thanhToan.TrangThai = TrangThaiThanhToan.DangXuLy;
                    await _context.SaveChangesAsync();

                    // Chuyển hướng đến gateway thanh toán
                    return Redirect(paymentResponse.PaymentUrl);
                }
                else
                {
                    TempData["Error"] = paymentResponse.Message;
                    return RedirectToAction("Payment", new { sessionId });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi xử lý thanh toán");
                TempData["Error"] = "Có lỗi xảy ra khi xử lý thanh toán";
                return RedirectToAction("Payment", new { sessionId });
            }
        }

        // GET: Booking/PaymentCallback - Xử lý callback từ gateway
        public async Task<IActionResult> PaymentCallback(string sessionId)
        {
            var bookingStep = GetBookingFromSession(sessionId);
            if (bookingStep == null)
            {
                TempData["Error"] = "Phiên đặt vé không hợp lệ";
                return RedirectToAction("Search");
            }

            try
            {
                // Lấy parameters từ query string
                var parameters = Request.Query.ToDictionary(x => x.Key, x => x.Value.ToString());

                // Kiểm tra nếu là demo payment
                bool isDemo = parameters.ContainsKey("demo") && parameters["demo"] == "true";
                bool paymentSuccess = false;

                if (isDemo)
                {
                    // Xử lý demo payment
                    paymentSuccess = parameters.ContainsKey("status") && parameters["status"] == "success";
                }
                else
                {
                    // Xử lý payment thật qua service
                    var paymentResponse = await _paymentService.ProcessPaymentCallbackAsync(
                        bookingStep.MaGiaoDich ?? "", parameters);
                    paymentSuccess = paymentResponse.Success;
                }

                if (paymentSuccess)
                {
                    // Cập nhật trạng thái thanh toán
                    var thanhToan = await _context.ThanhToans
                        .FirstOrDefaultAsync(t => t.MaGiaoDich == bookingStep.MaGiaoDich);

                    if (thanhToan != null)
                    {
                        thanhToan.TrangThai = TrangThaiThanhToan.ThanhCong;
                        thanhToan.ThoiGianThanhToan = DateTime.Now;
                        thanhToan.MaPhanHoi = parameters.ContainsKey("transaction_id") ? parameters["transaction_id"] : bookingStep.MaGiaoDich;
                        thanhToan.ThongTinPhanHoi = isDemo ? "Thanh toán demo thành công" : "Thanh toán thành công";

                        await _context.SaveChangesAsync();
                    }

                    TempData["Success"] = "Thanh toán thành công! Vé của bạn đã được xác nhận.";
                    return RedirectToAction("Confirmation", new { sessionId });
                }
                else
                {
                    string errorMessage = parameters.ContainsKey("error") ? parameters["error"] : "Thanh toán thất bại";
                    TempData["Error"] = "Thanh toán thất bại: " + errorMessage;
                    return RedirectToAction("Payment", new { sessionId });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi xử lý callback thanh toán");
                TempData["Error"] = "Có lỗi xảy ra khi xử lý thanh toán";
                return RedirectToAction("Payment", new { sessionId });
            }
        }

        // GET: Booking/PaymentCancel - Xử lý khi hủy thanh toán
        public IActionResult PaymentCancel(string sessionId)
        {
            TempData["Warning"] = "Bạn đã hủy thanh toán. Vui lòng thử lại.";
            return RedirectToAction("Payment", new { sessionId });
        }

        // GET: Booking/DemoPayment - Trang demo thanh toán
        public IActionResult DemoPayment(string method, string transactionId, decimal amount, string orderInfo, string returnUrl, string cancelUrl)
        {
            ViewBag.PaymentMethod = method;
            ViewBag.TransactionId = transactionId;
            ViewBag.Amount = amount;
            ViewBag.OrderInfo = orderInfo;
            ViewBag.ReturnUrl = returnUrl;
            ViewBag.CancelUrl = cancelUrl;

            return View();
        }

        // GET: Booking/Confirmation - Xác nhận đặt vé thành công
        public async Task<IActionResult> Confirmation(string sessionId)
        {
            var bookingStep = GetBookingFromSession(sessionId);
            if (bookingStep == null || string.IsNullOrEmpty(bookingStep.MaVe))
            {
                TempData["Error"] = "Không tìm thấy thông tin đặt vé";
                return RedirectToAction("Search");
            }

            // Lấy thông tin chuyến xe và ghế từ booking step
            var chuyenXe = await _context.ChuyenXes
                .Include(c => c.Xe)
                .Include(c => c.TuyenDuong)
                .FirstOrDefaultAsync(c => c.ChuyenXeId == bookingStep.ChuyenXeId);

            var choNgoi = await _context.ChoNgois
                .FirstOrDefaultAsync(c => c.ChoNgoiId == bookingStep.ChoNgoiId);

            if (chuyenXe == null || choNgoi == null)
            {
                TempData["Error"] = "Không tìm thấy thông tin chuyến xe hoặc ghế";
                return RedirectToAction("Search");
            }

            var thanhToan = await _context.ThanhToans
                .FirstOrDefaultAsync(t => t.MaGiaoDich == bookingStep.MaGiaoDich);

            // Gửi email và SMS xác nhận
            bool emailSent = false;
            bool smsSent = false;

            if (!string.IsNullOrEmpty(bookingStep.Email))
            {
                try
                {
                    var tripInfo = $"{chuyenXe.DiemDiDisplay} - {chuyenXe.DiemDenDisplay}";
                    var seatInfo = choNgoi.SoGhe ?? "Chưa chọn chỗ cụ thể";

                    emailSent = await _emailService.SendTicketConfirmationAsync(
                        bookingStep.Email, bookingStep.TenKhach, bookingStep.MaVe, tripInfo, seatInfo,
                        bookingStep.TongTien, chuyenXe.NgayKhoiHanh);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Lỗi khi gửi email xác nhận");
                }
            }

            if (!string.IsNullOrEmpty(bookingStep.SoDienThoai))
            {
                try
                {
                    var tripInfo = $"{chuyenXe.DiemDiDisplay} - {chuyenXe.DiemDenDisplay}";
                    var seatInfo = choNgoi.SoGhe ?? "Chưa chọn chỗ cụ thể";

                    smsSent = await _smsService.SendTicketConfirmationSMSAsync(
                        bookingStep.SoDienThoai, bookingStep.TenKhach, bookingStep.MaVe, tripInfo, seatInfo,
                        bookingStep.TongTien, chuyenXe.NgayKhoiHanh);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Lỗi khi gửi SMS xác nhận");
                }
            }

            var viewModel = new BookingConfirmationViewModel
            {
                MaVe = bookingStep.MaVe,
                TenKhach = bookingStep.TenKhach,
                SoDienThoai = bookingStep.SoDienThoai,
                Email = bookingStep.Email,
                GiaVe = bookingStep.TongTien,
                ChuyenXe = chuyenXe,
                ChoNgoi = choNgoi,
                ThanhToan = thanhToan,
                EmailSent = emailSent,
                SMSSent = smsSent,
                BookingReference = bookingStep.MaVe
            };

            // Xóa session booking
            HttpContext.Session.Remove($"Booking_{sessionId}");

            return View(viewModel);
        }
    }
}
