@model TicketDetailViewModel
@{
    ViewData["Title"] = "Chi tiết vé";
}

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary fw-bold">
                    <i class="bi bi-ticket-detailed me-2"></i>Chi tiết vé
                </h2>
                <div>
                    <a asp-action="Index" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left me-2"></i>Quay lại
                    </a>
                    @if (Model.CanCancel)
                    {
                        <button type="button" class="btn btn-outline-danger" 
                                data-bs-toggle="modal" data-bs-target="#cancelModal">
                            <i class="bi bi-x-circle me-2"></i>Hủy vé
                        </button>
                    }
                    @if (Model.CanReview)
                    {
                        <a asp-controller="Review" asp-action="Create" asp-route-id="@Model.Ve.VeId" 
                           class="btn btn-warning">
                            <i class="bi bi-star me-2"></i>Đ<PERSON>h giá
                        </a>
                    }
                </div>
            </div>

            <div class="row">
                <!-- Thông tin vé chính -->
                <div class="col-lg-8">
                    <div class="card border-0 shadow-sm mb-4">
                        <div class="card-header bg-primary text-white">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="bi bi-ticket-perforated me-2"></i>Thông tin vé
                                </h5>
                                <span class="badge bg-light text-dark">@Model.Ve.MaVe</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <!-- Trạng thái -->
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">Trạng thái</label>
                                    <div>
                                        @{
                                            var badgeClass = Model.Ve.TrangThai switch
                                            {
                                                TrangThaiVe.DaDat => "bg-primary",
                                                TrangThaiVe.DaThanhToan => "bg-success",
                                                TrangThaiVe.DaHoanThanh => "bg-info",
                                                TrangThaiVe.DaHuy => "bg-danger",
                                                _ => "bg-secondary"
                                            };
                                        }
                                        <span class="badge @badgeClass fs-6">@Model.TrangThaiDisplay</span>
                                        @if (Model.ThoiGianConLai.TotalHours > 0 && Model.Ve.TrangThai == TrangThaiVe.DaDat)
                                        {
                                            <span class="badge bg-warning text-dark ms-2">
                                                <i class="bi bi-clock me-1"></i>
                                                Còn @(Model.ThoiGianConLai.Days > 0 ? $"{Model.ThoiGianConLai.Days} ngày" : $"{Model.ThoiGianConLai.Hours}h {Model.ThoiGianConLai.Minutes}m")
                                            </span>
                                        }
                                    </div>
                                </div>

                                <!-- Ngày đặt -->
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">Ngày đặt vé</label>
                                    <div class="fw-semibold">@Model.Ve.NgayDat.ToString("dd/MM/yyyy HH:mm")</div>
                                </div>

                                <!-- Tuyến đường -->
                                <div class="col-12 mb-3">
                                    <label class="form-label text-muted">Tuyến đường</label>
                                    <div class="d-flex align-items-center">
                                        <div class="text-center">
                                            <div class="fw-bold fs-5 text-primary">@Model.Ve.ChuyenXe?.DiemDi</div>
                                            <small class="text-muted">Điểm đi</small>
                                        </div>
                                        <div class="flex-grow-1 text-center mx-3">
                                            <i class="bi bi-arrow-right text-primary fs-3"></i>
                                            <div class="small text-muted">@(Model.Ve.ChuyenXe?.TuyenDuong?.KhoangCach ?? 0) km</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="fw-bold fs-5 text-primary">@Model.Ve.ChuyenXe?.DiemDen</div>
                                            <small class="text-muted">Điểm đến</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Thời gian khởi hành -->
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">Thời gian khởi hành</label>
                                    <div class="fw-semibold fs-5 text-success">
                                        @Model.Ve.ChuyenXe?.NgayKhoiHanh.ToString("dd/MM/yyyy HH:mm")
                                    </div>
                                </div>

                                <!-- Giá vé -->
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">Giá vé</label>
                                    <div class="fw-bold fs-4 text-success">
                                        @string.Format("{0:N0}", Model.Ve.GiaVe) VNĐ
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Thông tin hành khách -->
                    <div class="card border-0 shadow-sm mb-4">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="bi bi-person-fill me-2"></i>Thông tin hành khách
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">Tên hành khách</label>
                                    <div class="fw-semibold">@Model.Ve.TenKhach</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">Số điện thoại</label>
                                    <div class="fw-semibold">@Model.Ve.SoDienThoai</div>
                                </div>
                                @if (!string.IsNullOrEmpty(Model.Ve.Email))
                                {
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label text-muted">Email</label>
                                        <div class="fw-semibold">@Model.Ve.Email</div>
                                    </div>
                                }
                                @if (!string.IsNullOrEmpty(Model.Ve.GhiChu))
                                {
                                    <div class="col-12 mb-3">
                                        <label class="form-label text-muted">Ghi chú</label>
                                        <div class="fw-semibold">@Model.Ve.GhiChu</div>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>

                    <!-- Thông tin xe và ghế -->
                    <div class="card border-0 shadow-sm mb-4">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0">
                                <i class="bi bi-bus-front me-2"></i>Thông tin xe và ghế
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">Nhà xe</label>
                                    <div class="fw-semibold">@Model.Ve.ChuyenXe?.Xe?.NhaXe</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">Loại xe</label>
                                    <div class="fw-semibold">@Model.Ve.ChuyenXe?.Xe?.LoaiXe</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">Biển số xe</label>
                                    <div class="fw-semibold">@Model.Ve.ChuyenXe?.Xe?.BienSo</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">Số ghế</label>
                                    <div class="fw-bold fs-4 text-primary">@Model.Ve.ChoNgoi?.SoGhe</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Thông tin thanh toán -->
                    @if (Model.ThanhToan != null)
                    {
                        <div class="card border-0 shadow-sm mb-4">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0">
                                    <i class="bi bi-credit-card me-2"></i>Thông tin thanh toán
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label text-muted">Phương thức</label>
                                        <div class="fw-semibold">@Model.ThanhToan.PhuongThucThanhToan.GetDisplayName()</div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label text-muted">Trạng thái</label>
                                        <div>
                                            @{
                                                var paymentBadge = Model.ThanhToan.TrangThai switch
                                                {
                                                    TrangThaiThanhToan.ThanhCong => "bg-success",
                                                    TrangThaiThanhToan.ThatBai => "bg-danger",
                                                    TrangThaiThanhToan.DangXuLy => "bg-warning",
                                                    _ => "bg-secondary"
                                                };
                                            }
                                            <span class="badge @paymentBadge">@Model.ThanhToan.TrangThai.GetDisplayName()</span>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label text-muted">Số tiền</label>
                                        <div class="fw-bold text-success">@string.Format("{0:N0}", Model.ThanhToan.SoTien) VNĐ</div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label text-muted">Thời gian thanh toán</label>
                                        <div class="fw-semibold">@Model.ThanhToan.NgayThanhToan.ToString("dd/MM/yyyy HH:mm")</div>
                                    </div>
                                    @if (!string.IsNullOrEmpty(Model.ThanhToan.MaGiaoDich))
                                    {
                                        <div class="col-12 mb-3">
                                            <label class="form-label text-muted">Mã giao dịch</label>
                                            <div class="fw-semibold font-monospace">@Model.ThanhToan.MaGiaoDich</div>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                    }

                    <!-- Đánh giá -->
                    @if (Model.DanhGia != null)
                    {
                        <div class="card border-0 shadow-sm mb-4">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="mb-0">
                                    <i class="bi bi-star-fill me-2"></i>Đánh giá của bạn
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-12 mb-3">
                                        <label class="form-label text-muted">Điểm đánh giá tổng thể</label>
                                        <div>
                                            @for (int i = 1; i <= 5; i++)
                                            {
                                                <i class="bi bi-star@(i <= Model.DanhGia.DiemDanhGia ? "-fill" : "") text-warning"></i>
                                            }
                                            <span class="ms-2 fw-semibold">@Model.DanhGia.DiemDanhGia/5</span>
                                        </div>
                                    </div>
                                    
                                    @if (Model.DanhGia.DanhGiaTaiXe.HasValue || Model.DanhGia.DanhGiaXe.HasValue || Model.DanhGia.DanhGiaDichVu.HasValue)
                                    {
                                        <div class="col-12 mb-3">
                                            <div class="row">
                                                @if (Model.DanhGia.DanhGiaTaiXe.HasValue)
                                                {
                                                    <div class="col-md-4">
                                                        <small class="text-muted">Tài xế</small>
                                                        <div>
                                                            @for (int i = 1; i <= 5; i++)
                                                            {
                                                                <i class="bi bi-star@(i <= Model.DanhGia.DanhGiaTaiXe ? "-fill" : "") text-warning small"></i>
                                                            }
                                                        </div>
                                                    </div>
                                                }
                                                @if (Model.DanhGia.DanhGiaXe.HasValue)
                                                {
                                                    <div class="col-md-4">
                                                        <small class="text-muted">Xe</small>
                                                        <div>
                                                            @for (int i = 1; i <= 5; i++)
                                                            {
                                                                <i class="bi bi-star@(i <= Model.DanhGia.DanhGiaXe ? "-fill" : "") text-warning small"></i>
                                                            }
                                                        </div>
                                                    </div>
                                                }
                                                @if (Model.DanhGia.DanhGiaDichVu.HasValue)
                                                {
                                                    <div class="col-md-4">
                                                        <small class="text-muted">Dịch vụ</small>
                                                        <div>
                                                            @for (int i = 1; i <= 5; i++)
                                                            {
                                                                <i class="bi bi-star@(i <= Model.DanhGia.DanhGiaDichVu ? "-fill" : "") text-warning small"></i>
                                                            }
                                                        </div>
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                    }

                                    @if (!string.IsNullOrEmpty(Model.DanhGia.NoiDungDanhGia))
                                    {
                                        <div class="col-12 mb-3">
                                            <label class="form-label text-muted">Nội dung đánh giá</label>
                                            <div class="fw-semibold">@Model.DanhGia.NoiDungDanhGia</div>
                                        </div>
                                    }

                                    <div class="col-12">
                                        <small class="text-muted">
                                            <i class="bi bi-calendar3 me-1"></i>
                                            Đánh giá lúc: @Model.DanhGia.ThoiGianDanhGia.ToString("dd/MM/yyyy HH:mm")
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>

                <!-- QR Code và thao tác -->
                <div class="col-lg-4">
                    <div class="card border-0 shadow-sm mb-4">
                        <div class="card-header bg-dark text-white text-center">
                            <h5 class="mb-0">
                                <i class="bi bi-qr-code me-2"></i>QR Code
                            </h5>
                        </div>
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <img src="@Url.Action("QRCode", new { id = Model.Ve.VeId })" 
                                     alt="QR Code" class="img-fluid" style="max-width: 200px;" />
                            </div>
                            <p class="text-muted small">
                                Quét mã QR này khi lên xe để xác nhận vé
                            </p>
                            <button type="button" class="btn btn-outline-primary btn-sm" 
                                    onclick="downloadQR()">
                                <i class="bi bi-download me-2"></i>Tải QR Code
                            </button>
                        </div>
                    </div>

                    <!-- Thao tác nhanh -->
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-secondary text-white">
                            <h5 class="mb-0">
                                <i class="bi bi-gear me-2"></i>Thao tác
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                @if (Model.CanCancel)
                                {
                                    <button type="button" class="btn btn-outline-danger" 
                                            data-bs-toggle="modal" data-bs-target="#cancelModal">
                                        <i class="bi bi-x-circle me-2"></i>Hủy vé
                                    </button>
                                }

                                @if (Model.CanReview)
                                {
                                    <a asp-controller="Review" asp-action="Create" asp-route-id="@Model.Ve.VeId" 
                                       class="btn btn-warning">
                                        <i class="bi bi-star me-2"></i>Đánh giá chuyến đi
                                    </a>
                                }

                                <button type="button" class="btn btn-outline-info" onclick="printTicket()">
                                    <i class="bi bi-printer me-2"></i>In vé
                                </button>

                                <button type="button" class="btn btn-outline-success" onclick="shareTicket()">
                                    <i class="bi bi-share me-2"></i>Chia sẻ
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal hủy vé -->
<div class="modal fade" id="cancelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Xác nhận hủy vé</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form asp-action="Cancel" asp-route-id="@Model.Ve.VeId" method="post">
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        Bạn có chắc chắn muốn hủy vé <strong>@Model.Ve.MaVe</strong>?
                        <br><small>Tiền sẽ được hoàn lại trong 3-5 ngày làm việc.</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="lyDoHuy" class="form-label">Lý do hủy (tùy chọn)</label>
                        <textarea name="lyDoHuy" id="lyDoHuy" class="form-control" rows="3" 
                                  placeholder="Nhập lý do hủy vé..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-x-circle me-2"></i>Xác nhận hủy
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function downloadQR() {
            const link = document.createElement('a');
            link.href = '@Url.Action("QRCode", new { id = Model.Ve.VeId })';
            link.download = 'QRCode_@(Model.Ve.MaVe).txt';
            link.click();
        }

        function printTicket() {
            window.print();
        }

        function shareTicket() {
            if (navigator.share) {
                navigator.share({
                    title: 'Vé xe @Model.Ve.MaVe',
                    text: 'Thông tin vé xe từ @Model.Ve.ChuyenXe?.DiemDi đến @Model.Ve.ChuyenXe?.DiemDen',
                    url: window.location.href
                });
            } else {
                // Fallback: copy to clipboard
                navigator.clipboard.writeText(window.location.href).then(() => {
                    alert('Đã copy link vé vào clipboard!');
                });
            }
        }
    </script>
}

<style>
    @@media print {
        .btn, .modal, nav, footer {
            display: none !important;
        }
        
        .card {
            border: 1px solid #000 !important;
            box-shadow: none !important;
        }
    }
</style>
